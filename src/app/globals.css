@import "tailwindcss";

:root {
  --bg: #c1fffe;
  --bg-secondary: #1a1a3a;
  --text: #000000;
  --text-secondary: #252525;
  --accent: #d6a4f3;
  --primary: #6366f1;
  --secondary: #8b5cf6;
  --border: #2a2a4a;
  --card-bg: #84626bf2;
}

* {
  box-sizing: border-box;
}



html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  background: var(--bg);
  color: var(--text);
  font-family: var(--font-geist-sans), system-ui, sans-serif;
  line-height: 1.6;
  min-height: 100vh;
}

/* 確保inline樣式不被覆蓋 - 移除可能衝突的全域樣式 */

/* 自定義樣式類別 */
.text-accent {
  color: var(--accent) !important;
}

.text-secondary {
  color: var(--text-secondary) !important;
}

.bg-accent {
  background-color: var(--accent) !important;
}

.bg-card {
  background-color: var(--card-bg) !important;
}

.border-custom {
  border-color: var(--border) !important;
}

/* 按鈕樣式 */
.btn-primary {
  background-color: var(--primary);
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
  background-color: rgba(99, 102, 241, 0.9);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
  background-color: var(--secondary);
  color: white;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-secondary:hover {
  background-color: rgba(139, 92, 246, 0.9);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.btn-accent {
  background-color: var(--accent);
  color: #1f2937;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.2s;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-accent:hover {
  background-color: rgba(214, 164, 243, 0.9);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 卡片樣式 */
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

/* 漸變文字 */
.gradient-text {
  background: linear-gradient(135deg, var(--accent), var(--primary));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 滾動條樣式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg);
}

::-webkit-scrollbar-thumb {
  background: rgb(0, 186, 9);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary);
}

/* 動畫 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

/* 響應式設計 */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

